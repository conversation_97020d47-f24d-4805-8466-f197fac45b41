[{"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_b2c", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "customer_name", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca type Simplified ( B2C ) ", "length": 0, "mandatory_depends_on": null, "modified": "2023-12-25 06:40:20.788938", "module": "Zatca2024", "name": "Customer-custom_b2c", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "Not submitted", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_uuid", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "tax_id", "is_system_generated": 0, "is_virtual": 0, "label": "UUID", "length": 0, "mandatory_depends_on": null, "modified": "2023-12-26 06:47:49.383701", "module": "Zatca2024", "name": "Sales Invoice-custom_uuid", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "Not Submitted", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_status", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "due_date", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Status", "length": 0, "mandatory_depends_on": null, "modified": "2023-12-26 06:48:03.659055", "module": "Zatca2024", "name": "Sales Invoice-custom_zatca_status", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_tax_category", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca_status", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Tax Category", "length": 0, "mandatory_depends_on": null, "modified": "2024-05-01 07:54:32.149357", "module": "Zatca2024", "name": "Sales Invoice-custom_zatca_tax_category", "no_copy": 0, "non_negative": 0, "options": "Standard\nZero Rated\nExempted\nServices outside scope of tax / Not subject to VAT", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_exemption_reason_code", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca_tax_category", "is_system_generated": 0, "is_virtual": 0, "label": "Exemption Reason Code", "length": 0, "mandatory_depends_on": null, "modified": "2024-05-01 12:02:35.929408", "module": "Zatca2024", "name": "Sales Invoice-custom_exemption_reason_code", "no_copy": 0, "non_negative": 0, "options": "VATEX-SA-29\nVATEX-SA-29-7\nVATEX-SA-30\nVATEX-SA-32\nVATEX-SA-33\nVATEX-SA-34-1\nVATEX-SA-34-2\nVATEX-SA-34-3\nVATEX-SA-34-4\nVATEX-SA-34-5\nVATEX-SA-35\nVATEX-SA-36 \nVATEX-SA-EDU\nVATEX-SA-HEA \nVATEX-SA-MLTRY\nVATEX-SA-OOS", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}]