[project]
name = "zatca2024"
authors = [
    { name = "ERPGulf", email = "<EMAIL>"}
]
description = "Saudi Zatca phase-2 implementation according to zatca 2024 documents"
requires-python = ">=3.10"
readme = "README.md"
dynamic = ["version"]
dependencies = [
    # "frappe~=15.0.0" # Installed and managed by bench.
]

[build-system]
requires = ["flit_core >=3.4,<4"]
build-backend = "flit_core.buildapi"

# These dependencies are only installed when developer mode is enabled
[tool.bench.dev-dependencies]
# package_name = "~=1.1.0"
