
This repo we are going to archive , as there is new version with advanced features has been released. Please use the new version available here https://github.com/ERPGulf/zatca_erpgulf


Saudi VAT Phase-2 implementation according to new Zatca Guidelines 2024

Features for Multiple companies added

License mit#

This app is compliant with the new Zatca e-invoicing document published in 2024.

You can follow standard Frappe methods for installing apps. Please see it here below bench 

get-app https://github.com/ERPGulf/saudi_phase2_api.git

bench --site yoursite.erpgulf.com install-app saudi-phase2-api

bench --site yoursite.erpgulf.com migrate

Goto Help->About and make sure you have Zatca app installaed.

We have published a video tutorial on how to use this . ( https://www.youtube.com/watch?v=P0ChplXoKYg )

Please contact <EMAIL> for implementation support or customization.

Husna
