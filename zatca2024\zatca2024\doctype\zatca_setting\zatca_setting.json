{"actions": [], "allow_rename": 1, "creation": "2024-12-15 12:02:35.671013", "doctype": "DocType", "engine": "InnoDB", "field_order": ["company", "detailsotp_tab", "zatca_invoice_enabled", "background", "send_invoice_to_zatca", "select", "create_csr", "attach_xml_with_invoice", "attach_xml_with_qr_code", "attach_qr_code_doctype", "attach_e_invoice_send_status_with_invoice", "sdk_root", "pih", "urls__api_endpoints_section", "sandbox_url", "simulation_url", "production_url", "csid_and_tokens_tab", "otp", "csid_attach", "basic_auth", "compliance_request_id", "zatca_compliance_check_check_all_options_below_section", "validation_type", "sample_invoice_to_test", "check_compliance", "validation_results", "production_token_generation_section", "production_csid", "basic_auth_production"], "fields": [{"fieldname": "detailsotp_tab", "fieldtype": "Section Break", "label": "Details and OTP"}, {"default": "0", "fieldname": "zatca_invoice_enabled", "fieldtype": "Check", "label": "Zatca invoice enabled"}, {"fieldname": "send_invoice_to_zatca", "fieldtype": "Select", "label": "Send invoice to zatca", "options": "Live\nBatches"}, {"fieldname": "create_csr", "fieldtype": "<PERSON><PERSON>", "label": "Create CSR"}, {"default": "0", "fieldname": "attach_xml_with_invoice", "fieldtype": "Check", "label": "Attach XML with invoice"}, {"default": "0", "fieldname": "attach_xml_with_qr_code", "fieldtype": "Check", "label": "Attach XML with QR code"}, {"default": "0", "fieldname": "attach_qr_code_doctype", "fieldtype": "Check", "label": "Attach QR code doctype"}, {"default": "0", "fieldname": "attach_e_invoice_send_status_with_invoice", "fieldtype": "Check", "label": "Attach e_invoice send status with invoice"}, {"fieldname": "otp", "fieldtype": "Data", "label": "OTP"}, {"fieldname": "pih", "fieldtype": "Long Text", "label": "PIH"}, {"fieldname": "urls__api_endpoints_section", "fieldtype": "Section Break", "label": "URLs / API EndPoints"}, {"fieldname": "select", "fieldtype": "Select", "label": "Select", "options": "Simulation\nSandbox\nProduction"}, {"fieldname": "sandbox_url", "fieldtype": "Data", "label": "Sandbox URL"}, {"fieldname": "simulation_url", "fieldtype": "Data", "label": "Simulation URL"}, {"fieldname": "production_url", "fieldtype": "Data", "label": "Production URL"}, {"fieldname": "csid_and_tokens_tab", "fieldtype": "Section Break", "label": "Compliance CSID Generation"}, {"fieldname": "csid_attach", "fieldtype": "<PERSON><PERSON>", "label": "Generate Compliance CSID"}, {"fieldname": "production_csid", "fieldtype": "<PERSON><PERSON>", "label": "Generate Production CSID"}, {"fieldname": "basic_auth", "fieldtype": "Long Text", "label": "Basic Auth from CSID"}, {"fieldname": "compliance_request_id", "fieldtype": "Long Text", "label": "Compliance request id "}, {"fieldname": "basic_auth_production", "fieldtype": "Long Text", "label": "Basic Auth from production"}, {"fieldname": "sdk_root", "fieldtype": "Data", "label": "SDK Root"}, {"default": "0", "fieldname": "background", "fieldtype": "Check", "label": "Send e-invoice background"}, {"fieldname": "production_token_generation_section", "fieldtype": "Section Break", "label": "Production  CSID  Generation"}, {"fieldname": "validation_type", "fieldtype": "Select", "label": "Validation Type", "options": "Simplified Invoice\nStandard Invoice\nSimplified Credit Note\nStandard Credit Note\nSimplified Debit Note\nStandard Debit Note"}, {"fieldname": "zatca_compliance_check_check_all_options_below_section", "fieldtype": "Section Break", "label": "Zatca Compliance Check. Check all options below."}, {"fieldname": "sample_invoice_to_test", "fieldtype": "Data", "label": "Sample Invoice Number to Test", "options": "0001"}, {"fieldname": "validation_results", "fieldtype": "Small Text", "hidden": 1, "label": "Validation results"}, {"fieldname": "check_compliance", "fieldtype": "<PERSON><PERSON>", "label": "Check Compliance"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company"}], "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2024-04-17 14:20:47.989970", "modified_by": "Administrator", "module": "Zatca2024", "name": "Zatca setting", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}